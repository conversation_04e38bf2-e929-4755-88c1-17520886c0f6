import pickle
import numpy as np

# 从示例数据中的 SMILES 创建简单的字典
smiles_list = [
    'CCO',
    'CC(C)CC1=CC=C(C=C1)C(C)C(=O)O',
    'CN1C=NC2=C1C(=O)N(C(=O)N2C)C',
    'CC1=CC=C(C=C1)C2=CC(=NN2C3=CC=C(C=C3)S(=O)(=O)N)C(F)(F)F',
    'C1=CC=C(C=C1)C2=CN=CN=C2',
    'CC(C)(C)OC(=O)N1CCC(CC1)N2C=NC3=C(N=CN=C32)N',
    'COC1=CC=C(C=C1)C2=CN=C(N=C2N3CCOCC3)C4=CC=CC=C4',
    'C1=CC=C2C(=C1)C(=CN2)CC(C(=O)O)N',
    'CC1=CC=CC=C1C2=CC=C(C=C2)C(=O)N3CCN(CC3)C4=CC=CC=N4',
    'CN(C)C1=NC=NC2=C1C=CN2C3=CC=CC=C3'
]

mol_dict = {}
for smiles in smiles_list:
    # 创建假的嵌入向量 (seq_len, 384)
    seq_len = len(smiles) + 2
    embedding = np.random.randn(seq_len, 384).astype(np.float32)
    mol_dict[smiles] = embedding

with open('mol_dict.pkl', 'wb') as f:
    pickle.dump(mol_dict, f)

print(f"Created mol_dict.pkl with {len(mol_dict)} compounds")

#!/usr/bin/env python3
"""
测试数据加载功能
"""

import os
import sys
import numpy as np
import pandas as pd
import pickle

def create_minimal_features():
    """创建最小的特征文件用于测试"""
    print("Creating minimal feature files...")
    
    # 创建分子字典
    os.makedirs('data/bindingdb/compound', exist_ok=True)
    
    # 从示例数据获取 SMILES
    train_df = pd.read_csv('data/bindingdb_train_sample.csv')
    test_df = pd.read_csv('data/bindingdb_test_sample.csv')
    all_smiles = list(set(train_df['compound_iso_smiles'].tolist() + test_df['compound_iso_smiles'].tolist()))
    
    mol_dict = {}
    for smiles in all_smiles:
        seq_len = min(len(smiles) + 2, 100)  # 限制长度避免内存问题
        embedding = np.random.randn(seq_len, 384).astype(np.float32)
        mol_dict[smiles] = embedding
    
    with open('data/bindingdb/compound/mol_dict.pkl', 'wb') as f:
        pickle.dump(mol_dict, f)
    print(f"Created mol_dict.pkl with {len(mol_dict)} compounds")
    
    # 创建蛋白质特征
    os.makedirs('data/bindingdb/protein/train', exist_ok=True)
    os.makedirs('data/bindingdb/protein/test', exist_ok=True)
    
    # 获取唯一蛋白质
    all_proteins = pd.concat([train_df[['target_id', 'target_sequence']], 
                             test_df[['target_id', 'target_sequence']]]).drop_duplicates()
    
    for _, row in all_proteins.iterrows():
        protein_id = row['target_id']
        sequence = row['target_sequence']
        seq_len = min(len(sequence), 500)  # 限制长度
        embedding = np.random.randn(seq_len, 1280).astype(np.float32)
        
        # 保存到训练和测试目录
        np.save(f'data/bindingdb/protein/train/{protein_id}.npy', embedding)
        np.save(f'data/bindingdb/protein/test/{protein_id}.npy', embedding)
    
    print(f"Created protein features for {len(all_proteins)} proteins")

def test_data_loading():
    """测试数据加载"""
    print("\nTesting data loading...")
    
    try:
        from data import CPIDataset
        
        # 测试训练集
        print("Loading training dataset...")
        train_dataset = CPIDataset(
            'data/bindingdb_train.csv',  # 这会自动使用 sample 文件
            'data/bindingdb/compound',
            'data/bindingdb/protein/train'
        )
        print(f"Training dataset size: {len(train_dataset)}")
        
        # 测试第一个样本
        sample = train_dataset[0]
        print("Sample data shapes:")
        for key, value in sample.items():
            if hasattr(value, 'shape'):
                print(f"  {key}: {value.shape}")
            else:
                print(f"  {key}: {value}")
        
        # 测试测试集
        print("\nLoading test dataset...")
        test_dataset = CPIDataset(
            'data/bindingdb_test.csv',  # 这会自动使用 sample 文件
            'data/bindingdb/compound',
            'data/bindingdb/protein/test'
        )
        print(f"Test dataset size: {len(test_dataset)}")
        
        print("\n✅ Data loading test passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Data loading test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=== PMMR Data Loading Test ===")
    
    # 检查示例数据文件是否存在
    if not os.path.exists('data/bindingdb_train_sample.csv'):
        print("❌ Sample data files not found!")
        return
    
    # 创建特征文件
    create_minimal_features()
    
    # 测试数据加载
    success = test_data_loading()
    
    if success:
        print("\n🎉 All tests passed! You can now run:")
        print("python main.py --objective classification --dataset bindingdb --batch_size 2 --max_epochs 1")
    else:
        print("\n❌ Tests failed. Please check the error messages above.")

if __name__ == "__main__":
    main()

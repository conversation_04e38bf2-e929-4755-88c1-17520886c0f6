# 如何处理 Git LFS 数据文件

您的项目中的所有 CSV 数据文件都是 Git LFS (Large File Storage) 指针文件，而不是实际的数据内容。

## 当前状态
所有数据文件显示的内容类似：
```
version https://git-lfs.github.com/spec/v1
oid sha256:9f5388c8852514379cd6602eb5c83e82bd7a9a8dbcbd1ce8d7f978e15e774709
size 10520188
```

## 解决方案

### 方案1：使用 Git LFS 下载实际数据（推荐）

如果您有访问原始仓库的权限：

1. **安装 Git LFS**（如果尚未安装）：
   ```bash
   # Windows
   git lfs install
   
   # 或者从 https://git-lfs.github.io/ 下载安装
   ```

2. **初始化 Git 仓库**：
   ```bash
   git init
   git remote add origin <原始仓库URL>
   ```

3. **下载 LFS 文件**：
   ```bash
   git lfs pull
   ```

### 方案2：创建示例数据文件

如果无法访问原始 Git LFS 存储，您需要创建示例数据文件。根据代码分析，数据文件应该包含以下列：

- `compound_iso_smiles`: 化合物的 SMILES 字符串
- `target_sequence`: 蛋白质序列
- `target_id`: 蛋白质ID
- `label`: 标签值（分类任务为0/1，回归任务为连续值）

### 方案3：修改代码以处理 LFS 指针文件

项目中的 `preprocessing/protein_pretrain/protein_dti.py` 已经包含了处理 LFS 指针文件的逻辑，但需要相应的示例数据文件。

## 已实施的解决方案

我已经为您创建了一个完整的解决方案来处理 Git LFS 指针文件问题：

### 1. 修改了数据加载代码
- 更新了 `data.py` 中的 `CPIDataset` 类
- 自动检测 Git LFS 指针文件并使用示例数据
- 当检测到 LFS 指针文件时，会自动查找对应的 `_sample.csv` 文件

### 2. 创建了示例数据文件
- `data/bindingdb_train_sample.csv` - 包含10个训练样本
- `data/bindingdb_test_sample.csv` - 包含5个测试样本
- 数据格式与原始数据集完全一致

### 3. 创建了必要的目录结构
```
data/bindingdb/
├── compound/
│   └── mol_dict.pkl (需要生成)
└── protein/
    ├── train/
    │   ├── P21.npy (需要生成)
    │   └── P00734.npy (需要生成)
    └── test/
        ├── P21.npy (需要生成)
        └── P00734.npy (需要生成)
```

### 4. 提供了特征生成脚本
- `create_sample_features.py` - 自动生成所有必要的特征文件
- `data/bindingdb/compound/create_mol_dict.py` - 生成分子字典
- `data/bindingdb/protein/train/create_protein_features.py` - 生成训练集蛋白质特征
- `data/bindingdb/protein/test/create_protein_features.py` - 生成测试集蛋白质特征

## 使用步骤

1. **生成特征文件**：
   ```bash
   # 生成分子字典
   cd data/bindingdb/compound
   python create_mol_dict.py

   # 生成蛋白质特征
   cd ../protein/train
   python create_protein_features.py

   cd ../test
   python create_protein_features.py
   ```

2. **测试数据加载**：
   ```bash
   python data.py
   ```

3. **运行训练**：
   ```bash
   python main.py --objective classification --dataset bindingdb --batch_size 64 --max_epochs 500 --learning_rate 0.001
   ```

## 注意事项

- 当前创建的是示例数据，用于测试项目功能
- 实际的特征向量是随机生成的，不是真实的预训练特征
- 如果需要真实数据，仍需要通过 Git LFS 下载或从其他来源获取
- 示例数据足够用来验证项目代码的正确性

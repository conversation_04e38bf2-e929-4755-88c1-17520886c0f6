#!/usr/bin/env python3
"""
创建示例特征文件用于测试 PMMR 项目
"""

import pickle
import numpy as np
import pandas as pd
import os

def create_sample_mol_dict():
    """创建示例分子字典"""
    # 从示例数据中获取所有 SMILES
    train_df = pd.read_csv('data/bindingdb_train_sample.csv')
    test_df = pd.read_csv('data/bindingdb_test_sample.csv')
    
    all_smiles = list(set(train_df['compound_iso_smiles'].tolist() + test_df['compound_iso_smiles'].tolist()))
    
    # 创建假的嵌入向量（实际应该使用 ChemBERTa）
    mol_dict = {}
    for smiles in all_smiles:
        # 创建随机的嵌入向量，维度为 (seq_len, 384)
        seq_len = len(smiles) + 2  # 加上 [CLS] 和 [SEP] token
        embedding = np.random.randn(seq_len, 384).astype(np.float32)
        mol_dict[smiles] = embedding
    
    # 保存字典
    os.makedirs('data/bindingdb/compound', exist_ok=True)
    with open('data/bindingdb/compound/mol_dict.pkl', 'wb') as f:
        pickle.dump(mol_dict, f)
    
    print(f"Created mol_dict.pkl with {len(mol_dict)} compounds")
    return mol_dict

def create_sample_protein_features():
    """创建示例蛋白质特征"""
    train_df = pd.read_csv('data/bindingdb_train_sample.csv')
    test_df = pd.read_csv('data/bindingdb_test_sample.csv')
    
    # 获取所有唯一的蛋白质ID
    train_proteins = train_df[['target_id', 'target_sequence']].drop_duplicates()
    test_proteins = test_df[['target_id', 'target_sequence']].drop_duplicates()
    
    # 为训练集蛋白质创建特征
    os.makedirs('data/bindingdb/protein/train', exist_ok=True)
    for _, row in train_proteins.iterrows():
        protein_id = row['target_id']
        sequence = row['target_sequence']
        
        # 创建假的蛋白质嵌入（实际应该使用 ESM）
        seq_len = len(sequence)
        embedding = np.random.randn(seq_len, 1280).astype(np.float32)  # ESM-2 的维度
        
        np.save(f'data/bindingdb/protein/train/{protein_id}.npy', embedding)
    
    # 为测试集蛋白质创建特征
    os.makedirs('data/bindingdb/protein/test', exist_ok=True)
    for _, row in test_proteins.iterrows():
        protein_id = row['target_id']
        sequence = row['target_sequence']
        
        # 创建假的蛋白质嵌入
        seq_len = len(sequence)
        embedding = np.random.randn(seq_len, 1280).astype(np.float32)
        
        np.save(f'data/bindingdb/protein/test/{protein_id}.npy', embedding)
    
    print(f"Created protein features for {len(train_proteins)} train and {len(test_proteins)} test proteins")

if __name__ == "__main__":
    print("Creating sample features for PMMR project...")
    
    # 创建分子字典
    create_sample_mol_dict()
    
    # 创建蛋白质特征
    create_sample_protein_features()
    
    print("Sample features created successfully!")
    print("\nNext steps:")
    print("1. You can now test the data loading with: python data.py")
    print("2. Or run the full training with: python main.py --objective classification --dataset bindingdb")
